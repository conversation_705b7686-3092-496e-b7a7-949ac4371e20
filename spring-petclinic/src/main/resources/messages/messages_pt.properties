welcome=Bem-vindo
required=E necessario
notFound=Nao foi encontrado
duplicate=Ja esta em uso
nonNumeric=Deve ser tudo numerico
duplicateFormSubmission=O envio duplicado de formulario nao e permitido
typeMismatch.date=Data invalida
typeMismatch.birthDate=Data de nascimento invalida
owner=Proprietário
firstName=Primeiro Nome
lastName=Sobrenome
address=Endereço
city=Cidade
telephone=Telefone
owners=Proprietários
addOwner=Adicionar proprietário
findOwner=Encontrar proprietário
findOwners=Encontrar proprietários
updateOwner=Atualizar proprietário
vets=Veterinários
name=Nome
specialties=Especialidades
none=nenhum
pages=páginas
first=Primeiro
next=Próximo
previous=Anterior
last=Último
somethingHappened=Algo aconteceu...
pets=Animais de estimação
home=Início
error=Erro
telephone.invalid=O número de telefone deve conter 10 dígitos
layoutTitle=PetClinic :: uma demonstração do Spring Framework
pet=Animal de estimação
birthDate=Data de nascimento
type=Tipo
previousVisits=Visitas anteriores
date=Data
description=Descrição
new=Novo
addVisit=Adicionar visita
editPet=Editar animal
ownerInformation=Informações do proprietário
visitDate=Data da visita
editOwner=Editar proprietário
addNewPet=Adicionar novo animal
petsAndVisits=Animais e visitas
