welcome=Bienvenido
required=Es requerido
notFound=No ha sido encontrado
duplicate=Ya se encuentra en uso
nonNumeric=<PERSON><PERSON>lo debe contener numeros
duplicateFormSubmission=No se permite el envío de formularios duplicados
typeMismatch.date=Fecha invalida
typeMismatch.birthDate=Fecha invalida
owner=Propietario
firstName=Nombre
lastName=Apellido
address=Dirección
city=Ciudad
telephone=Teléfono
owners=Propietarios
addOwner=Añadir propietario
findOwner=Buscar propietario
findOwners=Buscar propietarios
updateOwner=Actualizar propietario
vets=Veterinarios
name=Nombre
specialties=Especialidades
none=ninguno
pages=páginas
first=Primero
next=Siguiente
previous=Anterior
last=Último
somethingHappened=Algo pasó...
pets=Mascotas
home=Inicio
error=Error
telephone.invalid=El número de teléfono debe tener 10 dígitos
layoutTitle=PetClinic :: una demostración de Spring Framework
pet=Mascota
birthDate=Fecha de nacimiento
type=Tipo
previousVisits=Visitas anteriores
date=Fecha
description=Descripción
new=Nuevo
addVisit=Agregar visita
editPet=Editar mascota
ownerInformation=Información del propietario
visitDate=Fecha de visita
editOwner=Editar propietario
addNewPet=Agregar nueva mascota
petsAndVisits=Mascotas y visitas
