welcome=hoş geldiniz
required=gerekli
notFound=bulunamadı
duplicate=zaten kullanılıyor
nonNumeric=sadece sayısal olmalıdır
duplicateFormSubmission=Formun tekrar gönderilmesine izin verilmez
typeMismatch.date=geçersiz tarih
typeMismatch.birthDate=geçersiz tarih
owner=Sahip
firstName=Ad
lastName=Soyad
address=Adres
city=Şehir
telephone=Telefon
owners=Sahipler
addOwner=Sahip Ekle
findOwner=Sahip Bul
findOwners=Sahipleri Bul
updateOwner=Sahip Güncelle
vets=Veterinerler
name=İsim
specialties=Uzmanlıklar
none=yok
pages=sayfalar
first=İlk
next=Sonraki
previous=<PERSON>nce<PERSON>
last=Son
somethingHappened=Bir şey oldu...
pets=Evcil Hayvanlar
home=Ana Sayfa
error=Hata
telephone.invalid=Telefon numarası 10 basamaklı olmalıdır
layoutTitle=PetClinic :: bir Spring Framework demosu
pet=Evcil Hayvan
birthDate=Doğum Tarihi
type=Tür
previousVisits=Önceki Ziyaretler
date=Tarih
description=Açıklama
new=Yeni
addVisit=Z<PERSON>ret Ekle
editPet=Evcil Hayvanı Düzenle
ownerInformation=Sahip Bilgileri
visitDate=Ziyaret Tarihi
editOwner=Sahibi Düzenle
addNewPet=Yeni Evcil Hayvan Ekle
petsAndVisits=Evcil Hayvanlar ve Ziyaretler
