welcome=환영합니다
required=입력이 필요합니다
notFound=찾을 수 없습니다
duplicate=이미 존재합니다
nonNumeric=모두 숫자로 입력해야 합니다
duplicateFormSubmission=중복 제출은 허용되지 않습니다
typeMismatch.date=잘못된 날짜입니다
typeMismatch.birthDate=잘못된 날짜입니다
owner=소유자
firstName=이름
lastName=성
address=주소
city=도시
telephone=전화번호
owners=소유자 목록
addOwner=소유자 추가
findOwner=소유자 찾기
findOwners=소유자들 찾기
updateOwner=소유자 수정
vets=수의사
name=이름
specialties=전문 분야
none=없음
pages=페이지
first=첫 번째
next=다음
previous=이전
last=마지막
somethingHappened=문제가 발생했습니다...
pets=반려동물
home=홈
error=오류
telephone.invalid=전화번호는 10자리 숫자여야 합니다
layoutTitle=PetClinic :: Spring Framework 데모
pet=반려동물
birthDate=생년월일
type=종류
previousVisits=이전 방문
date=날짜
description=설명
new=새로운
addVisit=방문 추가
editPet=반려동물 수정
ownerInformation=소유자 정보
visitDate=방문 날짜
editOwner=소유자 수정
addNewPet=새 반려동물 추가
petsAndVisits=반려동물 및 방문
