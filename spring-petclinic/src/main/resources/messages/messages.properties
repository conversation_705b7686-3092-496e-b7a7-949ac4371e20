welcome=Welcome
required=is required
notFound=has not been found
duplicate=is already in use
nonNumeric=must be all numeric
duplicateFormSubmission=Duplicate form submission is not allowed
typeMismatch.date=invalid date
typeMismatch.birthDate=invalid date
owner=Owner
firstName=First Name
lastName=Last Name
address=Address
city=City
telephone=Telephone
owners=Owners
addOwner=Add Owner
findOwner=Find Owner
findOwners=Find Owners
updateOwner=Update Owner
vets=Veterinarians
name=Name
specialties=Specialties
none=none
pages=pages
first=First
next=Next
previous=Previous
last=Last
somethingHappened=Something happened...
pets=Pets
home=Home
error=Error
telephone.invalid=Telephone must be a 10-digit number
layoutTitle=PetClinic :: a Spring Framework demonstration
pet=Pet
birthDate=Birth Date
type=Type
previousVisits=Previous Visits
date=Date
description=Description
new=New 
addVisit=Add Visit
editPet=Edit Pet
ownerInformation=Owner Information
visitDate=Visit Date
editOwner=Edit Owner
addNewPet=Add New Pet
petsAndVisits=Pets and Visits
