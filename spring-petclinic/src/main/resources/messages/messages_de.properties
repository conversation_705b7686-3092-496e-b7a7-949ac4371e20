welcome=Willkommen
required=muss angegeben werden
notFound=wurde nicht gefunden
duplicate=ist bereits vergeben
nonNumeric=darf nur numerisch sein
duplicateFormSubmission=Wiederholtes Absenden des Formulars ist nicht erlaubt
typeMismatch.date=ung�ltiges Datum
typeMismatch.birthDate=ung�ltiges Datum
owner=Besitzer
firstName=Vorname
lastName=Nachname
address=Adresse
city=Stadt
telephone=Telefon
owners=Besitzer
addOwner=Besitzer hinzufügen
findOwner=Besitzer finden
findOwners=Besitzer suchen
updateOwner=Besitzer aktualisieren
vets=Tierärzte
name=Name
specialties=Fachgebiete
none=keine
pages=Seiten
first=Erste
next=Nächste
previous=Vorherige
last=Letzte
somethingHappened=Etwas ist passiert...
pets=Haustiere
home=Startseite
error=Fehler
telephone.invalid=Telefonnummer muss aus 10 Ziffern bestehen
layoutTitle=PetClinic :: eine Demonstration des Spring Frameworks
pet=Haustier
birthDate=Geburtsdatum
type=Typ
previousVisits=Frühere Besuche
date=Datum
description=Beschreibung
new=Neu
addVisit=Besuch hinzufügen
editPet=Haustier bearbeiten
ownerInformation=Besitzerinformationen
visitDate=Besuchsdatum
editOwner=Besitzer bearbeiten
addNewPet=Neues Haustier hinzufügen
petsAndVisits=Haustiere und Besuche
