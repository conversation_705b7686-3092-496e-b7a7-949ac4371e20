welcome=خوش آمدید
required=الزامی
notFound=یافت نشد
duplicate=قبلا استفاده شده
nonNumeric=باید عددی باشد
duplicateFormSubmission=ارسال تکراری فرم مجاز نیست
typeMismatch.date=تاریخ نامعتبر
typeMismatch.birthDate=تاریخ تولد نامعتبر
owner=مالک
firstName=نام
lastName=نام خانوادگی
address=آدرس
city=شهر
telephone=تلفن
owners=مالکان
addOwner=افزودن مالک
findOwner=یافتن مالک
findOwners=یافتن مالکان
updateOwner=ویرایش مالک
vets=دامپزشکان
name=نام
specialties=تخصص‌ها
none=هیچ‌کدام
pages=صفحات
first=اول
next=بعدی
previous=قبلی
last=آخر
somethingHappened=مشکلی پیش آمد...
pets=حیوانات خانگی
home=خانه
error=خطا
telephone.invalid=شماره تلفن باید ۱۰ رقمی باشد
layoutTitle=PetClinic :: یک نمایش از Spring Framework
pet=حیوان خانگی
birthDate=تاریخ تولد
type=نوع
previousVisits=ویزیت‌های قبلی
date=تاریخ
description=توضیحات
new=جدید
addVisit=افزودن ویزیت
editPet=ویرایش حیوان خانگی
ownerInformation=اطلاعات مالک
visitDate=تاریخ ویزیت
editOwner=ویرایش مالک
addNewPet=افزودن حیوان خانگی جدید
petsAndVisits=حیوانات و ویزیت‌ها
