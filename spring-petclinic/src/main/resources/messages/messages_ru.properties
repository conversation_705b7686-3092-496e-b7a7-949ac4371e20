welcome=Добро пожаловать
required=необходимо
notFound=не найдено
duplicate=уже используется
nonNumeric=должно быть все числовое значение
duplicateFormSubmission=Дублирование формы не допускается
typeMismatch.date=неправильная даные
typeMismatch.birthDate=неправильная дата
owner=Владелец
firstName=Имя
lastName=Фамилия
address=Адрес
city=Город
telephone=Телефон
owners=Владельцы
addOwner=Добавить владельца
findOwner=Найти владельца
findOwners=Найти владельцев
updateOwner=Обновить владельца
vets=Ветеринары
name=Имя
specialties=Специальности
none=нет
pages=страницы
first=Первый
next=Следующий
previous=Предыдущий
last=Последний
somethingHappened=Что-то пошло не так...
pets=Питомцы
home=Главная
error=Ошибка
telephone.invalid=Телефон должен содержать 10 цифр
layoutTitle=PetClinic :: демонстрация Spring Framework
pet=Питомец
birthDate=Дата рождения
type=Тип
previousVisits=Предыдущие визиты
date=Дата
description=Описание
new=Новый
addVisit=Добавить визит
editPet=Редактировать питомца
ownerInformation=Информация о владельце
visitDate=Дата визита
editOwner=Редактировать владельца
addNewPet=Добавить нового питомца
petsAndVisits=Питомцы и визиты
