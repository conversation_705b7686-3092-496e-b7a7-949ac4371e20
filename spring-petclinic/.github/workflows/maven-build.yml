# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/use-cases-and-examples/building-and-testing/building-and-testing-java-with-maven

name: Java CI with Ma<PERSON>

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        java: [ '17' ]

    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK ${{matrix.java}}
        uses: actions/setup-java@v4
        with:
          java-version: ${{matrix.java}}
          distribution: 'adopt'
          cache: maven
      - name: Build with <PERSON><PERSON> Wrapper
        run: ./mvnw -B verify
