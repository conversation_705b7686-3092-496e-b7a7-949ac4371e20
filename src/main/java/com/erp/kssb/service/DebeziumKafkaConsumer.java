package com.erp.kssb.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DebeziumKafkaConsumer {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @KafkaListener(topics = "debezium.fahasa.m_product", groupId = "kssb-consumer-group")
    public void consumeProductChanges(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset) {
        
        try {
            log.info("=== Received Debezium Message ===");
            log.info("Topic: {}", topic);
            log.info("Partition: {}", partition);
            log.info("Offset: {}", offset);
            
            // Parse the JSON message
            JsonNode messageNode = objectMapper.readTree(message);
            
            // Extract operation type (c=create, u=update, d=delete, r=read)
            String operation = messageNode.path("payload").path("op").asText();
            log.info("Operation: {}", operation);
            
            // Extract before and after data
            JsonNode beforeData = messageNode.path("payload").path("before");
            JsonNode afterData = messageNode.path("payload").path("after");
            
            log.info("=== Message Details ===");
            
            if (!beforeData.isNull() && !beforeData.isEmpty()) {
                log.info("Before: {}", beforeData.toString());
            }
            
            if (!afterData.isNull() && !afterData.isEmpty()) {
                log.info("After: {}", afterData.toString());
                
                // Extract specific product fields
                Long productId = afterData.path("m_product_id").asLong();
                String value = afterData.path("value").asText();
                String name = afterData.path("name").asText();
                
                log.info("Product ID: {}", productId);
                log.info("Value: {}", value);
                log.info("Name: {}", name);
            }
            
            // Extract timestamp
            long timestamp = messageNode.path("payload").path("ts_ms").asLong();
            log.info("Timestamp: {}", timestamp);
            
            log.info("=== Raw Message ===");
            log.info("{}", message);
            log.info("========================");
            
        } catch (Exception e) {
            log.error("Error processing Kafka message: {}", e.getMessage(), e);
            log.error("Raw message: {}", message);
        } finally {
            log.info("=== End of Message ===");
        }
    }
}
